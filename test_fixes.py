#!/usr/bin/env python3
"""
Test script to verify the session management and tap functionality fixes.
"""

import sys
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_w3c_actions_fix():
    """Test that W3C Actions API works without PointerInput.POINTER_TOUCH error"""
    logger.info("Testing W3C Actions API fix...")
    
    try:
        from selenium.webdriver.common.actions.pointer_input import PointerInput
        from selenium.webdriver.common.actions import interaction
        
        # This should not raise an AttributeError anymore
        pointer = PointerInput(interaction.POINTER_TOUCH, "touch")
        logger.info("✓ W3C Actions API import fix successful")
        return True
        
    except AttributeError as e:
        if "POINTER_TOUCH" in str(e):
            logger.error("✗ W3C Actions API fix failed - PointerInput.POINTER_TOUCH error still exists")
            return False
        else:
            logger.error(f"✗ Unexpected AttributeError: {e}")
            return False
    except Exception as e:
        logger.error(f"✗ Unexpected error testing W3C Actions API: {e}")
        return False

def test_airtest_availability():
    """Test AirTest availability"""
    logger.info("Testing AirTest availability...")
    
    try:
        from app_android.utils.appium_device_controller import AIRTEST_AVAILABLE
        
        if AIRTEST_AVAILABLE:
            logger.info("✓ AirTest is available")
            try:
                from airtest.core.api import touch
                logger.info("✓ AirTest touch function available")
            except ImportError:
                logger.warning("⚠ AirTest touch function not available")
        else:
            logger.warning("⚠ AirTest is not available")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Error testing AirTest: {e}")
        return False

def main():
    """Run tests"""
    logger.info("Testing critical fixes...")
    
    tests = [
        ("W3C Actions API Fix", test_w3c_actions_fix),
        ("AirTest Availability", test_airtest_availability),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    logger.info("\n" + "=" * 40)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nResult: {passed}/{total} tests passed")
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
