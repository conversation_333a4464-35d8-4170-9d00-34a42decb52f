#!/usr/bin/env python3
"""
Simple verification script for the critical fixes
"""

import sys
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_timeout_fix():
    """Verify that the timeout fix is in place"""
    logger.info("Verifying timeout fix...")
    
    try:
        # Read the appium_device_controller.py file
        with open('app_android/utils/appium_device_controller.py', 'r') as f:
            content = f.read()
        
        # Check that enhanced_timeout = max(timeout, 60) is removed
        if 'enhanced_timeout = max(timeout, 60)' in content:
            logger.error("✗ Enhanced timeout override still present")
            return False
        
        # Check that user-specified timeout is used
        if 'Using user-specified timeout' in content:
            logger.info("✓ User-specified timeout message found")
        else:
            logger.warning("⚠ User-specified timeout message not found")
        
        # Check that WebDriverWait uses timeout directly
        if 'Web<PERSON>river<PERSON>ait(self.driver, timeout)' in content:
            logger.info("✓ WebDriverWait uses timeout parameter directly")
            return True
        else:
            logger.error("✗ WebDriverWait still uses enhanced_timeout")
            return False
            
    except Exception as e:
        logger.error(f"Error verifying timeout fix: {e}")
        return False

def verify_airtest_primary_tap():
    """Verify that AirTest is used as primary tap method"""
    logger.info("Verifying AirTest primary tap...")
    
    try:
        # Read the appium_device_controller.py file
        with open('app_android/utils/appium_device_controller.py', 'r') as f:
            content = f.read()
        
        # Check for AirTest primary usage
        if 'Use AirTest as primary method for coordinate-based taps' in content:
            logger.info("✓ AirTest primary method comment found")
        else:
            logger.error("✗ AirTest primary method comment not found")
            return False
        
        # Check for AirTest primary implementation
        if 'Using AirTest primary tap' in content:
            logger.info("✓ AirTest primary tap implementation found")
        else:
            logger.error("✗ AirTest primary tap implementation not found")
            return False
        
        # Check that AirTest is tried before Appium
        airtest_pos = content.find('Using AirTest primary tap')
        appium_pos = content.find('Fallback to Appium if AirTest')
        
        if airtest_pos > 0 and appium_pos > airtest_pos:
            logger.info("✓ AirTest is tried before Appium fallback")
            return True
        else:
            logger.error("✗ AirTest is not prioritized over Appium")
            return False
            
    except Exception as e:
        logger.error(f"Error verifying AirTest primary tap: {e}")
        return False

def verify_imports():
    """Verify that required imports are available"""
    logger.info("Verifying imports...")
    
    try:
        # Test W3C Actions API import fix
        from selenium.webdriver.common.actions.pointer_input import PointerInput
        from selenium.webdriver.common.actions import interaction
        
        # This should not raise an AttributeError anymore
        pointer = PointerInput(interaction.POINTER_TOUCH, "touch")
        logger.info("✓ W3C Actions API import fix successful")
        
        return True
        
    except AttributeError as e:
        if "POINTER_TOUCH" in str(e):
            logger.error("✗ W3C Actions API fix failed - PointerInput.POINTER_TOUCH error still exists")
            return False
        else:
            logger.error(f"✗ Unexpected AttributeError: {e}")
            return False
    except Exception as e:
        logger.error(f"✗ Error testing imports: {e}")
        return False

def main():
    """Run verification"""
    logger.info("Starting critical fixes verification...")
    logger.info("=" * 50)
    
    tests = [
        ("Timeout Fix", verify_timeout_fix),
        ("AirTest Primary Tap", verify_airtest_primary_tap),
        ("Import Fix", verify_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("VERIFICATION SUMMARY:")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nResult: {passed}/{total} verifications passed")
    
    if passed == total:
        logger.info("🎉 All critical fixes verified successfully!")
        logger.info("✓ If-Else Steps timeout issue fixed")
        logger.info("✓ AirTest primary tap implementation working")
        return 0
    else:
        logger.warning(f"⚠ {total - passed} verification(s) failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
