2025-07-02 22:44:12,945 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 22:44:12,946 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 22:44:12,947 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-02 22:44:12,947 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-02 22:44:12,947 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-02 22:44:12,948 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-02 22:44:12,948 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-02 22:44:12,949 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-02 22:44:12,949 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-02 22:44:12,949 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-02 22:44:12,950 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-02 22:44:12,950 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-02 22:44:12,950 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-02 22:44:12,950 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-07-02 22:44:14,607 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
2025-07-02 22:44:14,607 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-07-02 22:44:14,607 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-07-02 22:44:14,608 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-02 22:44:14,610 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-02 22:44:14,610 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-02 22:44:14,660 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-02 22:44:15,205 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 22:44:15,225 - app - INFO - Using directories from config.py:
2025-07-02 22:44:15,225 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-02 22:44:15,225 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-02 22:44:15,225 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-02 22:44:15,228] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-02 22:44:15,229] INFO in database: Test_steps table schema updated successfully
[2025-07-02 22:44:15,229] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 22:44:15,229] INFO in database: Screenshots table schema updated successfully
[2025-07-02 22:44:15,229] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 22:44:15,231] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 22:44:15,231] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 22:44:15,231] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 22:44:15,231] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 22:44:15,231] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 22:44:15,231] INFO in database: Database initialized successfully
[2025-07-02 22:44:15,231] INFO in database: Checking initial database state...
[2025-07-02 22:44:15,238] INFO in database: Database state: 0 suites, 0 cases, 1534 steps, 1 screenshots, 1534 tracking entries
[2025-07-02 22:44:15,239] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-07-02 22:44:15,239] INFO in database: Test_steps table schema updated successfully
[2025-07-02 22:44:15,239] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 22:44:15,240] INFO in database: Screenshots table schema updated successfully
[2025-07-02 22:44:15,240] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 22:44:15,240] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 22:44:15,240] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 22:44:15,240] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 22:44:15,240] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 22:44:15,240] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 22:44:15,240] INFO in database: Database initialized successfully
[2025-07-02 22:44:15,240] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-07-02 22:44:15,241] INFO in database: step_idx column already exists in execution_tracking table
[2025-07-02 22:44:15,241] INFO in database: action_type column already exists in execution_tracking table
[2025-07-02 22:44:15,241] INFO in database: action_params column already exists in execution_tracking table
[2025-07-02 22:44:15,241] INFO in database: action_id column already exists in execution_tracking table
[2025-07-02 22:44:15,241] INFO in database: Successfully updated execution_tracking table schema
[2025-07-02 22:44:15,241] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-07-02 22:44:15,242] INFO in database: Screenshots table schema updated successfully
[2025-07-02 22:44:15,358] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-02 22:44:15,373] INFO in appium_device_controller: Appium server is running and ready
[2025-07-02 22:44:15,373] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-02 22:44:15,373] INFO in appium_device_controller: Connection monitoring started
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-02 22:44:15,406] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://************:8081
[2025-07-02 22:44:15,406] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-02 22:44:18,488] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-02 22:44:18,489] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:18,490] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:18] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:23,485] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:23,486] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:23] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:24,618] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET / HTTP/1.1" 200 -
[2025-07-02 22:44:24,674] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,675] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,676] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,679] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,683] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,686] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,687] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,688] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,690] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,693] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,695] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,697] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
[2025-07-02 22:44:24,698] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,703] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,704] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,712] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,713] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,717] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,717] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/action-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,722] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,722] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,723] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,727] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,728] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,736] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,739] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,740] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,741] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,747] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,750] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,753] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /static/js/main.js HTTP/1.1" 200 -
[2025-07-02 22:44:24,754] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,756] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,885] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-02 22:44:24,886] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:24,889] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/environments HTTP/1.1" 200 -
[2025-07-02 22:44:24,943] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:24,966] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/settings HTTP/1.1" 200 -
[2025-07-02 22:44:24,967] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:24,980] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-02 22:44:24,985] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-02 22:44:24,991] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-02 22:44:24,995] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-02 22:44:24,999] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:24] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:25,012] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-02 22:44:25,015] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/environments/2/variables HTTP/1.1" 200 -
[2025-07-02 22:44:25,021] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:25,031] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:25,041] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:25,048] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:44:25,102] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 22:44:25,103] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-02 22:44:25,110] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-02 22:44:25,134] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:25] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-02 22:44:26,167] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:26] "GET /api/devices HTTP/1.1" 200 -
[2025-07-02 22:44:28,657] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-02 22:44:28,663] INFO in appium_device_controller: Appium server is running and ready
[2025-07-02 22:44:28,663] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Connection monitoring started
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Using provided platform hint: Android
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True}
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True}
[2025-07-02 22:44:28,664] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 22:44:29,875] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:29,876] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:29,879] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:29,879] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:33,947] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-02 22:44:33,952] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-02 22:44:33,953] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-07-02 22:44:33,953] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-07-02 22:44:33,953] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-02 22:44:33,981] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-02 22:44:33,982] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-07-02 22:44:34,001] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8
[2025-07-02 22:44:34,001] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-07-02 22:44:34,001] INFO in appium_device_controller: Android version: 12.0
[2025-07-02 22:44:34,001] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-07-02 22:44:34,001] ERROR in appium_device_controller: Error running ADB command: can only concatenate list (not "str") to list
[2025-07-02 22:44:34,002] WARNING in appium_device_controller: Error setting up ADB: argument of type 'NoneType' is not iterable
[2025-07-02 22:44:34,002] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-02 22:44:34,002] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-02 22:44:34,002] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-02 22:44:34,004] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-02 22:44:34,004] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,005] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-02 22:44:34,005] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,005] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-02 22:44:34,006] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-02 22:44:34,006] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,006] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-02 22:44:34,007] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,008] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-02 22:44:34,009] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,010] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-02 22:44:34,011] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,011] INFO in action_factory: Registered action handler for 'wait'
[2025-07-02 22:44:34,012] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,012] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-02 22:44:34,014] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,014] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-02 22:44:34,015] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,016] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-02 22:44:34,018] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,018] INFO in action_factory: Registered action handler for 'text'
[2025-07-02 22:44:34,020] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-02 22:44:34,022] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,022] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-02 22:44:34,023] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,023] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-02 22:44:34,023] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,024] INFO in action_factory: Registered action handler for 'inputText'
[2025-07-02 22:44:34,026] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values_port_8081.db
[2025-07-02 22:44:34,026] INFO in global_values_db: Global values database initialized successfully
[2025-07-02 22:44:34,027] INFO in global_values_db: Using global values from config.py
[2025-07-02 22:44:34,027] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-02 22:44:34,031] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,031] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-02 22:44:34,032] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-02 22:44:34,032] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,032] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-02 22:44:34,033] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-07-02 22:44:34,034] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,034] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-07-02 22:44:34,035] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,035] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-02 22:44:34,036] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,036] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-02 22:44:34,037] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,038] INFO in action_factory: Registered action handler for 'tap'
[2025-07-02 22:44:34,039] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,039] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-07-02 22:44:34,040] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,040] INFO in action_factory: Registered action handler for 'clearText'
[2025-07-02 22:44:34,041] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-02 22:44:34,041] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,041] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-02 22:44:34,042] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-02 22:44:34,043] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,043] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-02 22:44:34,044] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,045] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-02 22:44:34,046] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-02 22:44:34,046] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,047] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-02 22:44:34,047] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,047] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-02 22:44:34,048] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-02 22:44:34,048] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,048] INFO in action_factory: Registered action handler for 'info'
[2025-07-02 22:44:34,049] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,049] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-02 22:44:34,050] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,050] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-02 22:44:34,052] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,052] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-02 22:44:34,053] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-02 22:44:34,053] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,053] INFO in action_factory: Registered action handler for 'exists'
[2025-07-02 22:44:34,054] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,054] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-02 22:44:34,055] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,055] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-02 22:44:34,056] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,056] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-02 22:44:34,056] INFO in action_factory: Registered action handler for 'test'
[2025-07-02 22:44:34,057] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,057] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-02 22:44:34,058] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-02 22:44:34,058] WARNING in base_action: Method selector not available, using default method selection
[2025-07-02 22:44:34,058] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-02 22:44:34,058] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'clearText', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-02 22:44:34,058] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-02 22:44:34,058] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-02 22:44:34,058] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-02 22:44:34,058] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'text': TextAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-02 22:44:34,059] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'clearText': ClearTextAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'test': TestAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-02 22:44:34,060] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-02 22:44:34,061] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-02 22:44:34,061] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-02 22:44:34,211] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-07-02 22:44:34,212] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:44:34,212] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:44:34,212] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-07-02 22:44:34,212 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 22:44:34,212] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-07-02 22:44:34,426 - image_matcher - ERROR - Error taking Android screenshot: Command '['adb', '-s', 'PJTCI7EMSSONYPU8', 'shell', 'screencap', '-p', '/sdcard/temp_screenshot.png']' returned non-zero exit status 1.
[2025-07-02 22:44:34,426] ERROR in image_matcher: Error taking Android screenshot: Command '['adb', '-s', 'PJTCI7EMSSONYPU8', 'shell', 'screencap', '-p', '/sdcard/temp_screenshot.png']' returned non-zero exit status 1.
[2025-07-02 22:44:34,427] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:44:34,427] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-02 22:44:34,876] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:34,878] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:34,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:34,881] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:39,875] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:39,876] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:39,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:39,881] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:39] "GET /api/reports/latest HTTP/1.1" 200 -
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-02 22:44:41,937] WARNING in appium_device_controller: Airtest screenshot failed: 'No available screen capture method found'
[2025-07-02 22:44:41,937] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:44:43,081] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:44:43,081] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:44:43,109] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:43] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-02 22:44:44,127] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:44:44,127] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-07-02 22:44:44,127 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 22:44:44,127] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-07-02 22:44:44,877] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:44,879] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:44,885] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:44,886] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:44] "GET /api/reports/latest HTTP/1.1" 200 -
2025-07-02 22:44:45,462 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751460284.png
[2025-07-02 22:44:45,462] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/debug_images/screenshot_1751460284.png
[2025-07-02 22:44:45,464] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:44:45,464] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:44:45,465] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:44:45,466] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:45] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1751460264872_76q2t0bcd_1751446057108_w77bm0twh&t=1751460284122 HTTP/1.1" 200 -
[2025-07-02 22:44:48,504] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:48] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 22:44:48,532] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:48] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-02 22:44:49,875] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:49,876] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:49,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:49,881] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:50,292] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:50] "GET /api/test_cases/load/Postcode_Flow_AU_ANDROID_20250702070922.json HTTP/1.1" 200 -
[2025-07-02 22:44:50,301] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:50] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-02 22:44:54,875] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:54,876] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:54,879] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:54,881] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:59,876] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:59,877] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:44:59,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:44:59,882] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:44:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:45:03,214] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:45:03] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-02 22:45:04,876] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:45:04,877] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:45:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:45:04,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-02 22:45:04,882] INFO in _internal: 127.0.0.1 - - [02/Jul/2025 22:45:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-02 22:45:05,957] INFO in player: Executing action: {'type': 'tap', 'timestamp': 1751460303207, 'x': 978, 'y': 371, 'method': 'coordinates'}
[2025-07-02 22:45:05,985] INFO in app: Using directories from config.py:
[2025-07-02 22:45:05,985] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[2025-07-02 22:45:05,985] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[2025-07-02 22:45:05,985] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-02 22:45:06,023] INFO in appium_device_controller: Tapping at coordinates: (978, 371)
[2025-07-02 22:45:06,023] INFO in appium_device_controller: Using TouchAction for Android at (978, 371)
[2025-07-02 22:45:06,023] INFO in appium_device_controller: Using emulated TouchAction implementation
[2025-07-02 22:45:06,023] ERROR in appium_device_controller: Error tapping with TouchAction: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:06,023] INFO in appium_device_controller: Using W3C Actions API for Android at (978, 371)
[2025-07-02 22:45:06,023] ERROR in appium_device_controller: Error using W3C Actions API: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:06,023] ERROR in appium_device_controller: Error in tap: Failed to tap: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:06,031] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3347, in tap
    actions.tap(x=x, y=y).perform()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 120, in perform
    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3361, in tap
    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3372, in tap
    raise Exception(f"Failed to tap: {w3c_err}")
Exception: Failed to tap: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

[2025-07-02 22:45:07,065] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:45:07,066] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:45:07,066] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:45:08,475] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png
[2025-07-02 22:45:08,476] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:45:08,476] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:45:08,496] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:45:08,496] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:45:09,557] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:45:09,558] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:45:09,558] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:45:09,558] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:45:13,125] DEBUG in appium_device_controller: Session ID: 99f1c86a-85ac-46c4-b3eb-2c982bda3ee3
[2025-07-02 22:45:13,212] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:45:43,952] DEBUG in appium_device_controller: Session ID: 99f1c86a-85ac-46c4-b3eb-2c982bda3ee3
[2025-07-02 22:45:44,037] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:45:55,494] INFO in appium_device_controller: Tapping at coordinates: (978, 371)
[2025-07-02 22:45:55,494] INFO in appium_device_controller: Using TouchAction for Android at (978, 371)
[2025-07-02 22:45:55,494] INFO in appium_device_controller: Using emulated TouchAction implementation
[2025-07-02 22:45:55,494] ERROR in appium_device_controller: Error tapping with TouchAction: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:55,494] INFO in appium_device_controller: Using W3C Actions API for Android at (978, 371)
[2025-07-02 22:45:55,494] ERROR in appium_device_controller: Error using W3C Actions API: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:55,495] ERROR in appium_device_controller: Error in tap: Failed to tap: type object 'PointerInput' has no attribute 'POINTER_TOUCH'
[2025-07-02 22:45:55,496] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3347, in tap
    actions.tap(x=x, y=y).perform()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 120, in perform
    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3361, in tap
    pointer = PointerInput(PointerInput.POINTER_TOUCH, "touch")
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3372, in tap
    raise Exception(f"Failed to tap: {w3c_err}")
Exception: Failed to tap: type object 'PointerInput' has no attribute 'POINTER_TOUCH'

[2025-07-02 22:45:56,503] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:45:56,503] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:45:56,504] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:45:56,504] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:45:57,540] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:45:57,542] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:45:57,543] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:45:57,543] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:45:57,555] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:45:57,555] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:45:58,603] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:45:58,604] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:45:58,604] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:45:58,604] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:12,322] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:12,322] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:13,127] DEBUG in appium_device_controller: Session ID: 99f1c86a-85ac-46c4-b3eb-2c982bda3ee3
[2025-07-02 22:46:13,131] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:46:13,454] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:13,455] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:13,455] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:13,455] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:14,987] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:14,987] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:14,987] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:14,987] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:16,205] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:16,206] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:16,207] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-02 22:46:16,207] INFO in appium_device_controller: suite_id: 
[2025-07-02 22:46:16,207] INFO in appium_device_controller: test_idx: 0
[2025-07-02 22:46:16,207] INFO in appium_device_controller: step_idx: 2
[2025-07-02 22:46:16,207] INFO in appium_device_controller: filename: placeholder.png
[2025-07-02 22:46:16,208] INFO in appium_device_controller: action_id: placeholder
[2025-07-02 22:46:16,209] INFO in appium_device_controller: Saved screenshot info to database for step 0_2
[2025-07-02 22:46:16,210] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:16,225] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:16,226] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:16,828] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-07-02 22:46:16,828] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-07-02 22:46:16,832] ERROR in appium_device_controller: Error in tap_element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:46:16,841] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3432, in tap_element
    element = WebDriverWait(self.driver, timeout, interval).until(
        EC.element_to_be_clickable((by_type, locator_value))
    )
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/wait.py", line 137, in until
    value = method(self._driver)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/expected_conditions.py", line 633, in _predicate
    target = driver.find_element(*target)  # grab element at locator
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 118, in find_element
    return self._execute_with_fallback('find_element', by, value)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 113, in _execute_with_fallback
    raise e
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 92, in _execute_with_fallback
    result = method(*args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 898, in find_element
    return self.execute(Command.FIND_ELEMENT, {"using": by, "value": value})["value"]
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/appium/webdriver/errorhandler.py", line 125, in check_response
    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
selenium.common.exceptions.InvalidSessionIdException: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)

[2025-07-02 22:46:16,841] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]' (timeout=5s, interval=0.5s)
[2025-07-02 22:46:16,841] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]'
[2025-07-02 22:46:16,844] ERROR in appium_device_controller: Error in tap_element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:46:16,846] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/appium_device_controller.py", line 3432, in tap_element
    element = WebDriverWait(self.driver, timeout, interval).until(
        EC.element_to_be_clickable((by_type, locator_value))
    )
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/wait.py", line 137, in until
    value = method(self._driver)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/expected_conditions.py", line 633, in _predicate
    target = driver.find_element(*target)  # grab element at locator
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 118, in find_element
    return self._execute_with_fallback('find_element', by, value)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 113, in _execute_with_fallback
    raise e
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/utils/healenium_wrapper.py", line 92, in _execute_with_fallback
    result = method(*args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 898, in find_element
    return self.execute(Command.FIND_ELEMENT, {"using": by, "value": value})["value"]
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/appium/webdriver/errorhandler.py", line 125, in check_response
    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
selenium.common.exceptions.InvalidSessionIdException: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)

[2025-07-02 22:46:17,324] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:17,325] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:17,325] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:17,325] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:17,853] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:17,854] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:17,854] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:17,854] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:18,955] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:18,956] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:18,957] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:18,957] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:20,060] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:20,060] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:20,060] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:20,060] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:21,094] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:21,095] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:21,096] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:21,096] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:22,227] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:22,227] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:22,228] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:22,228] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:23,384] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:23,386] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:23,387] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:23,387] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:24,501] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:24,502] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:24,502] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:24,502] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:25,569] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:25,573] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:25,574] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:25,575] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:29,475] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-02 22:46:29,480] INFO in appium_device_controller: Appium server is running and ready
[2025-07-02 22:46:29,480] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Connection monitoring started
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Using provided platform hint: Android
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True}
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True}
[2025-07-02 22:46:29,481] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-02 22:46:33,226] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-02 22:46:33,232] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-02 22:46:33,232] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-07-02 22:46:33,232] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-07-02 22:46:33,232] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-07-02 22:46:33,248] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-07-02 22:46:33,248] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Android version: 12.0
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-07-02 22:46:33,249] ERROR in appium_device_controller: Error running ADB command: can only concatenate list (not "str") to list
[2025-07-02 22:46:33,249] WARNING in appium_device_controller: Error setting up ADB: argument of type 'NoneType' is not iterable
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-02 22:46:33,249] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-02 22:46:33,252] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-07-02 22:46:33,253] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-07-02 22:46:33,421] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-07-02 22:46:33,421] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:33,421] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:33,422] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:33,422] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:34,480] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:34,481] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:34,482] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:34,482] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:35,528] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:35,528] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:36,529] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:36,531] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:36,531] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:36,531] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:52,248] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:52,248] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:53,247] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:53,249] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:53,250] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:53,250] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:54,773] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:54,773] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:54,773] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:54,773] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:55,854] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:55,855] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:55,856] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-02 22:46:55,856] INFO in appium_device_controller: suite_id: 
[2025-07-02 22:46:55,856] INFO in appium_device_controller: test_idx: 0
[2025-07-02 22:46:55,856] INFO in appium_device_controller: step_idx: 7
[2025-07-02 22:46:55,856] INFO in appium_device_controller: filename: placeholder.png
[2025-07-02 22:46:55,856] INFO in appium_device_controller: action_id: placeholder
[2025-07-02 22:46:55,858] INFO in appium_device_controller: Saved screenshot info to database for step 0_7
[2025-07-02 22:46:55,858] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:55,873] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:55,874] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:56,476] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-07-02 22:46:56,477] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-07-02 22:46:56,583] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:46:56,981] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:56,982] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:56,982] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:56,982] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:57,654] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:46:57,654] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:46:57,654] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:46:57,654] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:58,353] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:46:58,354] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:58,355] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:46:58,355] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:46:58,369] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:46:58,369] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:46:59,055] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:46:59,056] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:46:59,056] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:46:59,056] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:00,249] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:00,250] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:00,250] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:00,250] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:00,967] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:00,968] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:00,969] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:00,969] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:00,980] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:00,980] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:01,778] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:01,779] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:01,779] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:01,779] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:02,894] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:02,894] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:02,895] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:02,895] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:03,661] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:03,663] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:03,664] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:03,664] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:03,677] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:03,677] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:04,273] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.EditText[@resource-id="username"]' (timeout=10s, interval=0.5s)
[2025-07-02 22:47:04,273] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.EditText[@resource-id="username"]'
[2025-07-02 22:47:04,460] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:04,461] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:04,461] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:04,461] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:04,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:47:12,600] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:47:12,655] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:47:13,750] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:13,750] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:13,750] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:13,751] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:14,503] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:14,504] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:14,505] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:14,505] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:14,517] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:14,517] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:15,073] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-07-02 22:47:15,073] INFO in appium_device_controller: Using Airtest to input text: '<EMAIL>'
[2025-07-02 22:47:15,243] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:15,244] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:15,244] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:15,244] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:17,131] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:17,131] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:17,131] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:17,131] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:17,806] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:17,806] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:17,807] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:17,807] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:17,819] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:17,819] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:18,413] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.EditText[@resource-id="password"]' (timeout=10s, interval=0.5s)
[2025-07-02 22:47:18,413] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.EditText[@resource-id="password"]'
[2025-07-02 22:47:18,526] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:18,527] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:18,527] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:18,527] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:18,695] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:47:19,784] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:19,785] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:19,785] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:19,785] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:20,646] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:20,647] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:20,648] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:20,648] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:20,657] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:20,658] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:21,215] INFO in appium_device_controller: Inputting text 'Wonderbaby@5' directly (no locator)
[2025-07-02 22:47:21,216] INFO in appium_device_controller: Using Airtest to input text: 'Wonderbaby@5'
[2025-07-02 22:47:21,354] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:21,355] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:21,355] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:21,355] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:22,250] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:47:22,338] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:47:22,428] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:22,428] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:22,428] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:22,429] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:23,374] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:23,375] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:23,376] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:23,376] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:23,387] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:23,387] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:23,891] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:23,891] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:24,406] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:47:24,407] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-02 22:47:24,637] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:47:24,638] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-02 22:47:27,906] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:27,906] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:27,907] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:27,907] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:28,675] DEBUG in appium_device_controller: Current activity check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:47:28,679] DEBUG in appium_device_controller: Session check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:47:28,696] DEBUG in appium_device_controller: Orientation check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:47:28,697] WARNING in appium_device_controller: Periodic health check failed (1/7)
[2025-07-02 22:47:28,839] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:28,840] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:28,841] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:28,841] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:28,854] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:28,854] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:29,434] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:29,434] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:29,434] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:29,434] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:29,635] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:29,636] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:29,636] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:29,636] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:30,048] WARNING in appium_device_controller: Airtest screenshot failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
[2025-07-02 22:47:30,049] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:47:30,079] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:47:30,079] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-02 22:47:30,780] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:30,780] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:30,780] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:31,895] WARNING in appium_device_controller: Airtest screenshot failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
[2025-07-02 22:47:31,895] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:47:34,526] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:47:34,557] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:34,558] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:34,558] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:34,569] WARNING in appium_device_controller: Airtest screenshot failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
[2025-07-02 22:47:34,569] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:47:34,613] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:47:35,407] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:35,408] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:35,408] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:36,884] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:36,885] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:36,885] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:36,885] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:38,055] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:38,056] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:38,057] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:38,057] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:38,068] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:38,069] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:38,676] INFO in appium_device_controller: Tapping on element with xpath='//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]' (timeout=10s, interval=0.5s)
[2025-07-02 22:47:38,677] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]'
[2025-07-02 22:47:38,754] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:38,755] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:38,755] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:38,755] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:38,847] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:47:39,944] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:39,944] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:39,944] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:39,944] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:40,558] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:40,558] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:40,559] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:40,559] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:40,570] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:40,570] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:41,398] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:41,399] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:41,399] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:41,399] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:42,217] INFO in appium_device_controller: Inputting text '5000' directly (no locator)
[2025-07-02 22:47:42,217] INFO in appium_device_controller: Using Airtest to input text: '5000'
[2025-07-02 22:47:43,456] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:43,457] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:43,457] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:43,457] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:44,090] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:44,090] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:44,091] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:44,091] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:44,103] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:44,103] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:44,710] INFO in appium_device_controller: Tapping on element with xpath='//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]' (timeout=10s, interval=0.5s)
[2025-07-02 22:47:44,710] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]'
[2025-07-02 22:47:44,723] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:44,724] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:44,724] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:44,724] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:45,325] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:47:46,388] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:46,388] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:46,388] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:46,388] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:46,987] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:46,988] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:46,989] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:46,989] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:47,000] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:47,000] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:47,589] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:47,590] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:47,590] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:47,590] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:47,895] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:47:47,895] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-02 22:47:48,275] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to Airtest
[2025-07-02 22:47:48,275] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-07-02 22:47:49,059] WARNING in appium_device_controller: Airtest screenshot failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
[2025-07-02 22:47:49,059] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:47:49,470] WARNING in appium_device_controller: Airtest screenshot failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
[2025-07-02 22:47:49,470] INFO in appium_device_controller: Taking screenshot using Appium (attempt 1/3)
[2025-07-02 22:47:49,716] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:49,716] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:49,716] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:50,460] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:50,461] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:50,462] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:52,251] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:47:52,348] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:47:52,760] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:47:52,761] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:47:52,761] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:47:52,761] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:54,124] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:47:54,125] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:54,126] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:47:54,126] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:47:54,136] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:47:54,137] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:47:54,835] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:47:54,836] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:47:54,836] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:47:54,836] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:48:01,386] DEBUG in appium_device_controller: Current activity check failed: 'str' object is not callable
[2025-07-02 22:48:02,461] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:48:02,462] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:48:02,462] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:48:02,462] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:48:03,256] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:48:03,256] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:48:03,258] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:48:03,258] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:48:03,269] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:48:03,269] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:48:03,859] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.Button[@content-desc="btnSaveOrContinue"]' (timeout=10s, interval=0.5s)
[2025-07-02 22:48:03,859] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.Button[@content-desc="btnSaveOrContinue"]'
[2025-07-02 22:48:04,021] INFO in appium_device_controller: Element found, tapping on it
[2025-07-02 22:48:04,077] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:48:04,078] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:48:04,078] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:48:04,078] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:48:04,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:48:04,678] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:48:05,611] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots
[2025-07-02 22:48:05,612] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-02 22:48:05,612] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png (save_debug=False)
[2025-07-02 22:48:05,612] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:48:06,611] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/placeholder.png
[2025-07-02 22:48:06,612] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:48:06,613] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-02 22:48:06,613] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-02 22:48:06,623] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-07-02 22:48:06,623] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-07-02 22:48:07,213] INFO in appium_device_controller: Finding element with accessibility_id: btnUpdate, timeout=10s
[2025-07-02 22:48:07,213] DEBUG in appium_device_controller: Using enhanced timeout: 60s
[2025-07-02 22:48:07,682] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:48:07,684] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250702_224508/screenshots/latest.png
[2025-07-02 22:48:07,684] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-02 22:48:07,684] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-07-02 22:48:13,703] DEBUG in appium_device_controller: Current activity check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:48:13,705] DEBUG in appium_device_controller: Session check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:48:13,706] DEBUG in appium_device_controller: Orientation check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:48:13,706] WARNING in appium_device_controller: Periodic health check failed (2/7)
[2025-07-02 22:48:22,250] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:48:22,340] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:48:34,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:48:35,022] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:48:52,249] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:48:58,716] DEBUG in appium_device_controller: Current activity check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:48:58,718] DEBUG in appium_device_controller: Session check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:48:58,721] DEBUG in appium_device_controller: Orientation check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:48:58,721] WARNING in appium_device_controller: Periodic health check failed (3/7)
[2025-07-02 22:49:01,198] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:49:04,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:49:04,656] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:49:07,459] DEBUG in appium_device_controller: Presence condition failed, trying visibility condition
[2025-07-02 22:49:22,250] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:49:34,525] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:49:35,006] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:49:35,098] DEBUG in appium_device_controller: Current activity check failed: 'str' object is not callable
[2025-07-02 22:49:35,179] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:49:43,724] DEBUG in appium_device_controller: Current activity check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:49:43,727] DEBUG in appium_device_controller: Session check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:49:43,729] DEBUG in appium_device_controller: Orientation check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:49:43,729] WARNING in appium_device_controller: Periodic health check failed (4/7)
[2025-07-02 22:49:52,250] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:50:01,228] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:50:04,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:50:04,636] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:50:07,939] DEBUG in appium_device_controller: Visibility condition failed, trying clickable condition
[2025-07-02 22:50:22,250] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:50:28,733] DEBUG in appium_device_controller: Current activity check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-02 22:50:28,736] DEBUG in appium_device_controller: Session check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:50:28,738] DEBUG in appium_device_controller: Orientation check failed: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:91:12)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
[2025-07-02 22:50:28,738] WARNING in appium_device_controller: Periodic health check failed (5/7)
[2025-07-02 22:50:34,527] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:50:34,981] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:50:35,046] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
[2025-07-02 22:50:52,251] DEBUG in appium_device_controller: Session ID: 252ee01e-4344-4172-95ef-c1218b146888
[2025-07-02 22:50:52,338] DEBUG in appium_device_controller: Session is responsive (page_source check passed)
