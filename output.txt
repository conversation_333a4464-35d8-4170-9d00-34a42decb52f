2025-07-02 23:10:17,391 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 23:10:17,392 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-02 23:10:17,394 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-02 23:10:17,394 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-02 23:10:17,395 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-02 23:10:17,395 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-02 23:10:17,396 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-02 23:10:17,396 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-02 23:10:17,396 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-02 23:10:17,397 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-02 23:10:17,397 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-02 23:10:17,398 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-07-02 23:10:17,398 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4723, WDA: 8100) - preserving existing processes for multi-instance support
2025-07-02 23:10:17,398 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
